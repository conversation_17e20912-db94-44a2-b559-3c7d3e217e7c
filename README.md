Project Title: Envelopes 

Description
This Python script processes connection data from Excel files, aggregates values, identifies extreme forces, and provides recommendations for connection analysis. It is useful to analyze load envelopes in multiple connections.

Features:

    - Reads and processes data from multiple Excel files.
    - Identifies maximum and minimum forces per connection.
    - Groups results by connection sets.
    - Recommends critical connections for further analysis.
    - Outputs the Maximized Envelope Forces.

Choose a connection group when prompted.

Configuration (config.json)
Modify config.json to define:

    - sheet_name: Name of the sheet containing connection data.
    - sheet_name_members: Name of the sheet containing member data.
    - members_info: Filename of the member information Excel.
        - if there is no members_info.xlsm the code will ignore the consideration of the group on the gathering maximus!
    - connection_groups: Dictionary of named connection groups.

Dependencies
    - pandas for data processing
    - numpy for numerical operations
    - tabulate for table formatting
    - textwrap for text formatting
    - openpyxl for Excel handling

Error Handling
    - Checks if all required files exist before running.
    - Handles missing config.json or invalid JSON format.
    - Prompts the user to select a valid connection group if needed.
