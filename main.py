"""
Envelope Analysis Tool for Structural Engineering.

This module processes structural analysis data from Excel files to generate
envelope values for forces and moments for IDEA Statica. It reads connection data, processes
member information, and calculates maximum and minimum values for structural
elements with corresponding IDs.

Main functionality:
- Read and process structural analysis data from Excel files
- Calculate envelope values for forces and moments
- Generate maximized envelope values with safety factors
- Display results in formatted tables
- Identify critical connections based on force distribution

Author: ProIndustrial Engineering Team @2025
"""

import sys
import os
import textwrap
from collections import Counter
import json
import numpy as np
import pandas as pd
from tabulate import tabulate

########################################################################################

# Functions


def table_processor(df):  # Esta função estrutura as tabelas
    """
    Table Processor for structuring tables.

    Parameters
    ----------
    df : pandas.DataFrame
        It's a Pandas Data Frame table 
    
    Returns
    -------
    df : pandas.DataFrame
        Pandas Data Frame table formated and with rounded values.
    """
    cols_to_drop = ["Unnamed: 0", "Unnamed: 19"]
    cols = ["ID", "Node", "Element", "End", "CS", "MAT", "NMAX", "NMIN", "VYMAX", "VYMIN",
            "VZMAX", "VZMIN", "MXMAX", "MXMIN", "MYMAX", "MYMIN", "MZMAX", "MZMIN"]
    df = df.drop(columns=cols_to_drop, errors="ignore")
    df.columns = cols
    nan_indices = df[df.isna().all(axis=1)].index
    rows_to_drop = nan_indices.union(nan_indices + 1)
    df = df.drop(rows_to_drop, errors="ignore").reset_index(drop=True)
    df = df.round(2)
    return df


def get_extremes_dataframes(final_df, max_cols, min_cols):
    """
    Generate dataframes with extreme (max/min) values and their corresponding IDs.

    Parameters
    ----------
    final_df : pandas.DataFrame
        DataFrame containing the original values.
    max_cols : pandas.DataFrame
        DataFrame containing maximum values.
    min_cols : pandas.DataFrame
        DataFrame containing minimum values.

    Returns
    -------
    values_df : pandas.DataFrame
        DataFrame with the extreme values.
    ids_df : pandas.DataFrame
        DataFrame with the IDs of max and min values.
    """
    grouped = final_df.groupby('CS')  # Agrupamos por 'CS'

    values_list = []  # Para armazenar os valores máximos/mínimos
    ids_list = []  # Para armazenar os IDs correspondentes

    for cs, group in grouped:
        values_row = {'CS': cs}
        ids_row = {'CS': cs}

        for col in max_cols:  # Iteramos sobre colunas de máximo
            max_value = group[col].max()
            max_ids = group.loc[group[col] == max_value, 'ID'].tolist()
            values_row[col] = max_value
            ids_row[f"{col}_ID"] = ", ".join(max_ids)  # Concatenamos IDs

        for col in min_cols:  # Iteramos sobre colunas de mínimo
            min_value = group[col].min()
            min_ids = group.loc[group[col] == min_value, 'ID'].tolist()
            values_row[col] = min_value
            ids_row[f"{col}_ID"] = ", ".join(min_ids)  # Concatenamos IDs

        values_list.append(values_row)
        ids_list.append(ids_row)

    values_df = pd.DataFrame(values_list)  # DataFrame com valores
    ids_df = pd.DataFrame(ids_list)  # DataFrame com IDs

    return values_df, ids_df


def wrap_text(df, width=20):
    """
    Place the long texts of each cell in different lines.

    Parameters
    ----------
    df : pandas.DataFrame
        DataFrame containing the original values.

    Returns
    -------
    df : pandas.DataFrame
        DataFrame with the formated text in multiple lines.
    """

    return df.apply(
        lambda col: col.apply(
            lambda x: "\n".join(textwrap.wrap(str(x), width))
            if isinstance(x, str) else x
        )
    )


def check_files_exist(connections):
    """
    Check if the files exist.

    Parameters
    ----------
    df : list
        List containing the name of the files to be checked.

    Returns
    -------
    None.
    """

    for con in connections:
        file_path = os.path.join(os.getcwd(), '..', con + '.xlsm')
        file_path = os.path.abspath(file_path)
        # file_path = os.getcwd() + '\\data\\' + con + '.xlsm'  # Caminho do arquivo
        if not os.path.exists(file_path):
            print(
                f"⚠️ \033[91mError: File for connection {con} not found at {file_path}.\033[0m\n")
            # Para o script se o arquivo não for encontrado
            sys.exit("Exiting the script due to missing files.")
        # else:
            # print(f" File for connection {con} exists ✔️ ")
    print("\n")

#########################################################################################


def envelopes(sheet_name, sheet_name_members, members_info, connections, envelope_factor):

    """
    Get the envelopes for the connections.

    Parameters
    ----------
    sheet_name : list
        List containing the name of the files to be checked.
    sheet_name_members : list
        List containing the name of the files to be checked.
    members_info : list
        List containing the name of the files to be checked.
    connections : list
        List containing the name of the files to be checked.
    envelope_factor : list
        List containing the name of the files to be checked.

    Returns
    -------
    None.
    """
    print("\n🔹 Start of Data Reading")

    check_files_exist(connections)
    # print("\n")

    # Lê Connections

    global_connections = []

    for con in connections:
        file_path = os.path.join(os.getcwd(), '..', con + '.xlsm')
        file_path = os.path.abspath(file_path)
        # file_path = os.getcwd() + '\\data\\' + con + '.xlsm'  # Caminho do arquivo

        df = pd.read_excel(file_path, sheet_name, skiprows=14)
        print(f"🔹 \033[92mConnection {con} has been Read \033[0m✔️ ")
        df = df.round(2)
        df = table_processor(df)

        global_connections.append(df)

    # Junta todas as tabelas
    final_df = pd.concat(global_connections, ignore_index=True)

    print("\n \033[92mSuccessfully Created the Global DataFrame \033[0m✔️")

    # Lê Members

    file_memb_path = os.path.join(os.getcwd(), '..', members_info + '.xlsm')
    file_memb_path = os.path.abspath(file_memb_path)

    # file_memb_path = os.getcwd() + '\\data\\' + members_info + '.xlsm'  # Caminho do arquivo

    if os.path.exists(file_memb_path):
        memb = pd.read_excel(file_memb_path, sheet_name_members, skiprows=2)
        memb = memb.round(2)

     # Lista para armazenar os resultados
        info = []

        # Iterar sobre os valores de 'Element' nos DataFrames de 'global_connections'
        for df in global_connections:
            for val in df['Element']:  # Acessar a coluna 'Element' de cada DataFrame
                # Verificar se o valor de 'Element' existe na primeira coluna de 'memb'
                idx_in_memb = memb[memb.iloc[:, 0] == val].index
                if not idx_in_memb.empty:  # Verificar se encontrou algum índice
                    # Pega o primeiro índice encontrado
                    idx_in_memb = idx_in_memb[0]
                    # Extrair o valor da coluna 16
                    value_in_memb = memb.iloc[idx_in_memb, 15]
                    info.append(value_in_memb)
                else:
                    # Se o valor não for encontrado em 'memb', adicionar None
                    info.append(None)

        final_df['CS'] = final_df['CS'] + ' ' + info

    # print(final_df.head())

    # Envolvente = final_df[['CS']].drop_duplicates().reset_index(drop=True)
    max_cols = ['NMAX', 'VYMAX', 'VZMAX', 'MXMAX', 'MYMAX', 'MZMAX']
    min_cols = ['NMIN', 'VYMIN', 'VZMIN', 'MXMIN', 'MYMIN', 'MZMIN']

    # max_env_df = final_df.groupby('CS')[max_cols].agg(
    #    'max').reset_index()  # encontra o máximo por coluna para cada CS
    # min_env_df = final_df.groupby('CS')[min_cols].agg(
    #    'min').reset_index()  # encontra o máximo por coluna para cada CS

    # Criando o novo DataFrame
    values_df, ids_df = get_extremes_dataframes(final_df, max_cols, min_cols)
    ordered_columns = ["CS", "NMAX", "NMIN", "VYMAX", "VYMIN", "VZMAX",
                       "VZMIN", "MXMAX", "MXMIN", "MYMAX", "MYMIN", "MZMAX", "MZMIN"]

    values_df = values_df[ordered_columns]

    ordered_columns_with_id = []
    for col in ordered_columns:
        if col.endswith("MAX") or col.endswith("MIN"):
            # Adiciona _ID às colunas MAX e MIN
            ordered_columns_with_id.append(col + "_ID")
        else:
            ordered_columns_with_id.append(col)

    ids_df = ids_df[ordered_columns_with_id]

    ids_df[ordered_columns_with_id] = ids_df[ordered_columns_with_id].apply(lambda col: col.apply(
        # Remove duplicados
        lambda x: ', '.join(dict.fromkeys(map(str.strip, str(x).split(','))))))

    envonventemaximi_df = values_df.copy()

    # Aplica-se o ajuste com a multiplicação por 1.2 e o arredondamento para a unidade mais próxima
    envonventemaximi_df[max_cols + min_cols] = np.round(
        envonventemaximi_df[max_cols + min_cols] * float(envelope_factor[0])).astype(int)

    wrapped_ids_df = ids_df.copy()

    wrapped_ids_df.iloc[:, 1:] = wrap_text(
        wrapped_ids_df.iloc[:, 1:], width=15)  # Ajusta o texto em cada célula

    # Elimina od ID's cujo valor é próximo de zero da tabela de ID's Envolvente
    values_df_aux = values_df.apply(pd.to_numeric, errors='coerce')

    # Criar máscara para valores entre -0.5 e 0.5
    mask = values_df_aux.apply(lambda x: x.between(-0.25, 0.25))
    # mask = values_df_aux.apply(lambda x: x.between(-10, 10))

    indices = mask.stack()[mask.stack()].index
    row_numbers = indices.get_level_values(
        0).tolist()  # Números das linhas a alterar
    col_numbers = [values_df.columns.get_loc(col) for col in indices.get_level_values(
        1).tolist()]  # Número das colunas a alterar

    # Zeroa a célula da Matrix de ID's cujo valor é zero na Envolvente
    for row, col in zip(row_numbers, col_numbers):
        wrapped_ids_df.iloc[row, col] = ''

    #########################################################################################
    # Sugerir a ligação para Análise
    cols_to_check = wrapped_ids_df.columns[1:13]
    all_ids = wrapped_ids_df[cols_to_check].values.flatten()

    # Exibir os IDs e suas contagens
    all_ids = [str(id).replace('\n', '')
               for id in all_ids if str(id).strip() != '']
    all_ids = ', '.join(all_ids)
    segments = all_ids.split(',')
    segments = [segment.strip() for segment in segments]
    segment_counts = Counter(segments)
    most_common_segment = segment_counts.most_common(2)

    #########################################################################################

    print("\n📌 \033[1;32mEnvelope Corresponding IDs:\033[0m ")
    print(tabulate(wrapped_ids_df, headers='keys',
          tablefmt='fancy_grid', showindex=False))

    if len(most_common_segment) > 0:
        first_most_common_segment = most_common_segment[0][0]
        print(
            f"\n  → The connection with higher forces is: {first_most_common_segment}.")

    if len(most_common_segment) > 1:
        second_most_common_segment = most_common_segment[1][0]
        print(
            f"\n  → The second connection with higher forces is: {second_most_common_segment}.")

    print("\n📌 \033[1;32mEnvelope Values:\033[0m ")
    print(tabulate(values_df, headers='keys',
          tablefmt='fancy_grid', showindex=False))

    print(
        f"\n📌 \033[1;32mMaximized Envelope Values ({envelope_factor[0]} x Envelope):\033[0m")
    print(tabulate(envonventemaximi_df, headers='keys',
          tablefmt='fancy_grid', showindex=False))


def main():
    """
    Main function to run the script.
    """
    # Load configuration
    try:
        with open('config.json', 'r', encoding='utf-8') as f:
            config = json.load(f)
    except FileNotFoundError:
        print("Error: config.json file not found")
        sys.exit(1)
    except json.JSONDecodeError:
        print("Error: Invalid JSON in config.json")
        sys.exit(1)

    # Get configuration values
    sheet_name = config['sheet_name']
    sheet_name_members = config['sheet_name_members']
    members_info = config['members_info']
    envelope_factor = config['envelope_factor']

    # If a connection group is specified as command line argument, use it
    # Otherwise use the last group in the config
    if len(sys.argv) > 1:
        group_name = sys.argv[1]
        if group_name not in config['connection_groups']:
            print(
                f"Error: Connection group '{group_name}' not found in config")
            print("Available groups:", ", ".join(
                config['connection_groups'].keys()))
            sys.exit(1)
        connections = config['connection_groups'][group_name]
    else:
        # Use the last group as default
        available_groups = list(config['connection_groups'].keys())
        print("\nAvailable connection groups:")
        for i, group in enumerate(available_groups, 1):
            print(f"{i}. {group}")

        while True:
            try:
                choice = input("\nSelect a connection group (enter number): ")
                index = int(choice) - 1
                if 0 <= index < len(available_groups):
                    group_name = available_groups[index]
                    connections = config['connection_groups'][group_name]
                    break
                else:
                    print(
                        f"Please enter a number between 1 and {len(available_groups)}")
            except ValueError:
                print("Please enter a valid number")

    envelopes(sheet_name, sheet_name_members,
              members_info, connections, envelope_factor)


if __name__ == "__main__":
    main()
